'use client';

import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { FiMenu, FiUser, FiLogOut } from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Componente de Header Responsivo
 * 
 * Proporciona un header que se adapta entre móvil y escritorio:
 * - Móvil: Botón hamburguesa + Logo + Acciones de usuario
 * - Desktop: Logo + Información de usuario + Acciones
 * 
 * Siguiendo los patrones definidos en ARCHITECTURE.md
 */

interface ResponsiveHeaderProps {
  onMobileMenuToggle: () => void;
  isMobileMenuOpen: boolean;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({
  onMobileMenuToggle,
  isMobileMenuOpen
}) => {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
    }
  };

  const handleProfileClick = () => {
    router.push('/profile');
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
        <div className="flex justify-between items-center">
          
          {/* Sección izquierda - Botón hamburguesa (solo móvil) + Logo */}
          <div className="flex items-center">
            {/* Botón hamburguesa - Solo visible en móvil */}
            <button
              onClick={onMobileMenuToggle}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mr-3"
              aria-label={isMobileMenuOpen ? 'Cerrar menú' : 'Abrir menú'}
              aria-expanded={isMobileMenuOpen}
            >
              <FiMenu className="w-6 h-6 text-gray-600" />
            </button>

            {/* Logo */}
            <div className="flex items-center">
              <Image
                src="/logo2.png"
                alt="OposiAI Logo"
                width={80}
                height={80}
                className="h-16 w-16 lg:h-20 lg:w-20 object-contain"
                priority
              />
            </div>
          </div>

          {/* Sección derecha - Información de usuario y acciones */}
          <div className="flex items-center space-x-2 lg:space-x-4">
            {user && (
              <>
                {/* Información de usuario - Oculta en móvil muy pequeño */}
                <div className="hidden sm:flex items-center space-x-3">
                  <div className="text-sm text-gray-600">
                    Hola, {user.email?.split('@')[0]}
                  </div>
                </div>

                {/* Botones de acción */}
                <div className="flex items-center space-x-1 lg:space-x-2">
                  {/* Botón de perfil */}
                  <button
                    onClick={handleProfileClick}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    title="Ver perfil"
                    aria-label="Ver perfil de usuario"
                  >
                    <FiUser className="w-5 h-5 text-gray-600" />
                  </button>

                  {/* Botón de cerrar sesión */}
                  <button
                    onClick={handleSignOut}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    title="Cerrar sesión"
                    aria-label="Cerrar sesión"
                  >
                    <FiLogOut className="w-5 h-5 text-gray-600" />
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default ResponsiveHeader;
