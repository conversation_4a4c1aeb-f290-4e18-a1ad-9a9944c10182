export interface TemarioPredefinido {
  id: string;
  nombre: string;
  descripcion: string;
  cuerpo: string;
  archivo: string;
  temas: TemaTemarioPredefinido[];
}

export interface TemaTemarioPredefinido {
  numero: number;
  titulo: string;
  descripcion?: string;
}

/**
 * Lista de temarios predefinidos disponibles
 */
export const TEMARIOS_PREDEFINIDOS: Omit<TemarioPredefinido, 'temas'>[] = [
  {
    id: 'c2_junta',
    nombre: 'Cuerpo General Auxiliar - Junta de Andalucía (C2)',
    descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andalucía',
    cuerpo: 'CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUCÍA (C2)',
    archivo: 'c2_junta.md'
  },
  {
    id: 'c1_junta',
    nombre: 'Cuerpo General de Administrativos (C1.1000)',
    descripcion: 'Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andalucía',
    cuerpo: 'CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)',
    archivo: 'c1_junta.md'
  },
  {
    "id": "a2_cga_junta_libre",
    "nombre": "Cuerpo de Gestión Administrativa (A2) - Administración General (Junta de Andalucía)",
    "descripcion": "Temario para oposiciones del Cuerpo de Gestión Administrativa, especialidad Administración General, de la Junta de Andalucía (Acceso Libre)",
    "cuerpo": "CUERPO DE GESTIÓN ADMINISTRATIVA, ESPECIALIDAD ADMINISTRACIÓN GENERAL (A2.1100)",
    "archivo": "a2_cga_junta_libre.md"
},
  {
    id: 'a1_csag_junta_libre',
    nombre: 'Cuerpo Superior de Administradores (A1) - Administradores Generales (Junta de Andalucía)',
    descripcion: 'Temario para oposiciones del Cuerpo Superior de Administradores, especialidad Administradores Generales, de la Junta de Andalucía (Acceso Libre)',
    cuerpo: 'CUERPO SUPERIOR DE ADMINISTRADORES, ESPECIALIDAD ADMINISTRADORES GENERALES (A1.1100)',
    archivo: 'a1_csag_junta_libre.md'
},
  {
    id: 'a1_2008_junta',
    nombre: 'Cuerpo Superior Facultativo - Farmacia (A1.2008)',
    descripcion: 'Temario completo para oposiciones del Cuerpo Superior Facultativo, opción Farmacia de la Junta de Andalucía',
    cuerpo: 'CUERPO SUPERIOR FACULTATIVO, OPCIÓN FARMACIA (A1.2008)',
    archivo: 'a1_2008_junta.md'
  },
  {
    id: 'a1_2019_junta',
    nombre: 'Cuerpo Superior Facultativo - Informática (A1.2019)',
    descripcion: 'Temario completo para oposiciones del Cuerpo Superior Facultativo, opción Informática de la Junta de Andalucía',
    cuerpo: 'CUERPO SUPERIOR FACULTATIVO, OPCIÓN INFORMÁTICA (A1.2019)',
    archivo: 'a1_2019_junta.md'
  },
  
  {
    id: 'c2_estado',
    nombre: 'Cuerpo General Auxiliar del Estado (C2)',
    descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar del Estado',
    cuerpo: 'CUERPO GENERAL AUXILIAR DEL ESTADO (C2)',
    archivo: 'c2_estado.md'
  },
  {
    id: 'c1_estado',
    nombre: 'Cuerpo General Administrativo de la Administración del Estado (C1)',
    descripcion: 'Temario para oposiciones del Cuerpo General Administrativo de la Administración del Estado',
    cuerpo: 'CUERPO GENERAL ADMINISTRATIVO DEL ESTADO (C1)',
    archivo: 'c1_estado.md'
  },
  {
    id: 'c1_tai_estado_libre',
    nombre: 'Cuerpo de Técnicos Auxiliares de Informática del Estado (C1)',
    descripcion: 'Temario para oposiciones del Cuerpo de Técnicos Auxiliares de Informática de la Administración del Estado (Ingreso Libre)',
    cuerpo: 'CUERPO DE TÉCNICOS AUXILIARES DE INFORMÁTICA DE LA ADMINISTRACIÓN DEL ESTADO (C1)',
    archivo: 'c1_tai_estado_libre.md'
  },
  {
    id: 'a2_gace_estado_libre',
    nombre: 'Cuerpo de Gestión de la Administración Civil del Estado (A2)',
    descripcion: 'Temario para oposiciones del Cuerpo de Gestión de la Administración Civil del Estado (Ingreso Libre)',
    cuerpo: 'CUERPO DE GESTIÓN DE LA ADMINISTRACIÓN CIVIL DEL ESTADO (A2)',
    archivo: 'a2_gace_estado_libre.md'
}, 
  {
    id: 'a2_gsiae_estado_libre',
    nombre: 'Cuerpo de Gestión de Sistemas e Informática del Estado (A2)',
    descripcion: 'Temario para oposiciones del Cuerpo de Gestión de Sistemas e Informática de la Administración del Estado (Ingreso Libre)',
    cuerpo: 'CUERPO DE GESTIÓN DE SISTEMAS E INFORMÁTICA DE LA ADMINISTRACIÓN DEL ESTADO (A2)',
    archivo: 'a2_gsiae_estado_libre.md'
}  
];

/**
 * Obtiene la lista de temarios predefinidos disponibles
 */
export function obtenerTemariosPredefinidos(): Omit<TemarioPredefinido, 'temas'>[] {
  return TEMARIOS_PREDEFINIDOS;
}

/**
 * Parsea el contenido de un archivo de temario y extrae los temas
 */
export function parsearTemario(contenido: string): TemaTemarioPredefinido[] {
  const temas: TemaTemarioPredefinido[] = [];
  const lineas = contenido.split('\n');

  for (let i = 0; i < lineas.length; i++) {
    const linea = lineas[i].trim();

    // Patrón 1: "Tema X." (formato estándar)
    let match = linea.match(/^Tema\s+(\d+)\.\s*(.+)$/);

    // Patrón 2: "X." (formato numérico simple)
    if (!match) {
      match = linea.match(/^(\d+)\.\s*(.+)$/);
    }

    if (match) {
      const numero = parseInt(match[1]);
      const titulo = match[2].trim();

      // Filtrar líneas que no son realmente temas (muy cortas o solo números)
      if (titulo.length > 10 && !titulo.match(/^[IVX]+\s*$/)) {
        temas.push({
          numero,
          titulo,
          descripcion: titulo.length > 100 ? titulo.substring(0, 100) + '...' : titulo
        });
      }
    }
  }
  return temas;
}

/**
 * Carga un temario predefinido desde el archivo correspondiente
 */
export async function cargarTemarioPredefinido(id: string): Promise<TemarioPredefinido | null> {
  try {
    const temarioInfo = TEMARIOS_PREDEFINIDOS.find(t => t.id === id);
    if (!temarioInfo) {
      console.error('Temario predefinido no encontrado:', id);
      return null;
    }

    // Cargar el contenido del archivo
    const response = await fetch(`/temarios/${temarioInfo.archivo}`);
    if (!response.ok) {
      console.error('Error al cargar archivo de temario:', response.status);
      return null;
    }

    const contenido = await response.text();
    const temas = parsearTemario(contenido);

    return {
      ...temarioInfo,
      temas
    };
  } catch (error) {
    console.error('Error al cargar temario predefinido:', error);
    return null;
  }
}

/**
 * Obtiene información básica de un temario predefinido sin cargar los temas
 */
export function obtenerInfoTemarioPredefinido(id: string): Omit<TemarioPredefinido, 'temas'> | null {
  return TEMARIOS_PREDEFINIDOS.find(t => t.id === id) || null;
}

/**
 * Valida que un temario predefinido tenga la estructura correcta
 */
export function validarTemarioPredefinido(temario: TemarioPredefinido): boolean {
  if (!temario.id || !temario.nombre || !temario.cuerpo) {
    return false;
  }

  if (!temario.temas || temario.temas.length === 0) {
    return false;
  }

  // Verificar que los temas tengan numeración consecutiva
  for (let i = 0; i < temario.temas.length; i++) {
    const tema = temario.temas[i];
    if (!tema.numero || !tema.titulo) {
      return false;
    }
    
    // Verificar numeración consecutiva (empezando desde 1)
    if (tema.numero !== i + 1) {
      console.warn(`Numeración no consecutiva en tema ${tema.numero}, esperado ${i + 1}`);
    }
  }

  return true;
}

/**
 * Convierte un temario predefinido al formato necesario para crear en la base de datos
 */
export function convertirTemarioParaCreacion(temario: TemarioPredefinido): {
  titulo: string;
  descripcion: string;
  tipo: 'completo' | 'temas_sueltos';
  temas: Array<{
    numero: number;
    titulo: string;
    descripcion?: string;
    orden: number;
  }>;
} {
  return {
    titulo: temario.nombre,
    descripcion: `${temario.descripcion}\n\nCuerpo: ${temario.cuerpo}`,
    tipo: 'completo', // Los temarios predefinidos siempre son completos
    temas: temario.temas.map((tema, index) => ({
      numero: tema.numero,
      titulo: tema.titulo,
      descripcion: tema.descripcion,
      orden: index + 1
    }))
  };
}

/**
 * Busca temarios predefinidos por texto
 */
export function buscarTemariosPredefinidos(busqueda: string): Omit<TemarioPredefinido, 'temas'>[] {
  if (!busqueda.trim()) {
    return TEMARIOS_PREDEFINIDOS;
  }

  const termino = busqueda.toLowerCase();
  return TEMARIOS_PREDEFINIDOS.filter(temario => 
    temario.nombre.toLowerCase().includes(termino) ||
    temario.descripcion.toLowerCase().includes(termino) ||
    temario.cuerpo.toLowerCase().includes(termino)
  );
}

/**
 * Obtiene estadísticas de un temario predefinido
 */
export async function obtenerEstadisticasTemarioPredefinido(id: string): Promise<{
  totalTemas: number;
  tipoTemario: string;
  cuerpo: string;
} | null> {
  try {
    const temario = await cargarTemarioPredefinido(id);
    if (!temario) {
      return null;
    }

    return {
      totalTemas: temario.temas.length,
      tipoTemario: 'Temario Completo Predefinido',
      cuerpo: temario.cuerpo
    };
  } catch (error) {
    console.error('Error al obtener estadísticas:', error);
    return null;
  }
}
